#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Deploy asset tag removal script to multiple computers

.DESCRIPTION
    This script deploys and executes the asset tag removal script on multiple computers.
    Supports various deployment methods including WinRM, PsExec, and network shares.

.PARAMETER ComputerNames
    Array of computer names or IP addresses to deploy to

.PARAMETER ComputerListFile
    Path to text file containing computer names (one per line)

.PARAMETER Method
    Deployment method: 'WinRM', 'PsExec', 'NetworkShare', 'Local'

.PARAMETER Credential
    Credentials for remote access (if not provided, will prompt)

.PARAMETER LogPath
    Path for deployment log file

.PARAMETER ScriptPath
    Path to the asset tag removal script

.EXAMPLE
    .\Deploy-AssetTagRemoval.ps1 -ComputerNames @("PC01", "PC02") -Method WinRM

.EXAMPLE
    .\Deploy-AssetTagRemoval.ps1 -ComputerListFile "computers.txt" -Method PsExec
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $false)]
    [String[]]
    $ComputerNames,
    
    [Parameter(Mandatory = $false)]
    [String]
    $ComputerListFile = "computers.txt",
    
    [Parameter(Mandatory = $false)]
    [ValidateSet('WinRM', 'PsExec', 'NetworkShare', 'Local')]
    [String]
    $Method = 'WinRM',
    
    [Parameter(Mandatory = $false)]
    [PSCredential]
    $Credential,
    
    [Parameter(Mandatory = $false)]
    [String]
    $LogPath = ".\deployment_log.txt",
    
    [Parameter(Mandatory = $false)]
    [String]
    $ScriptPath = ".\remove_asset_tag_simple.ps1",
    
    [Parameter(Mandatory = $false)]
    [Switch]
    $Force
)

function Write-Log {
    param(
        [String]$Message,
        [String]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Output $logMessage
    Add-Content -Path $LogPath -Value $logMessage
}

function Test-Prerequisites {
    Write-Log "Checking prerequisites..."
    
    # Check if script exists
    if (-not (Test-Path $ScriptPath)) {
        Write-Log "ERROR: Asset tag removal script not found at: $ScriptPath" "ERROR"
        return $false
    }
    
    # Check method-specific prerequisites
    switch ($Method) {
        'PsExec' {
            if (-not (Get-Command psexec.exe -ErrorAction SilentlyContinue)) {
                Write-Log "ERROR: PsExec not found. Download from Microsoft Sysinternals." "ERROR"
                return $false
            }
        }
        'WinRM' {
            # WinRM is built into Windows
        }
    }
    
    return $true
}

function Get-TargetComputers {
    $computers = @()
    
    if ($ComputerNames) {
        $computers = $ComputerNames
    }
    elseif (Test-Path $ComputerListFile) {
        $computers = Get-Content $ComputerListFile | Where-Object { 
            $_.Trim() -ne "" -and -not $_.StartsWith("#") -and -not $_.StartsWith("REM") 
        }
    }
    else {
        Write-Log "Creating sample computer list file: $ComputerListFile"
        @"
# List computer names or IP addresses, one per line
# Examples:
# COMPUTER01
# COMPUTER02
# *************
"@ | Out-File -FilePath $ComputerListFile -Encoding UTF8
        
        Write-Log "Sample computer list created. Please edit $ComputerListFile and run again."
        return @()
    }
    
    return $computers
}

function Deploy-ToComputer-WinRM {
    param([String]$ComputerName)
    
    try {
        Write-Log "Testing WinRM connectivity to $ComputerName..."
        
        $session = New-PSSession -ComputerName $ComputerName -Credential $Credential -ErrorAction Stop
        
        Write-Log "Copying script to $ComputerName..."
        Copy-Item -Path $ScriptPath -Destination "C:\temp\" -ToSession $session -Force
        
        Write-Log "Executing script on $ComputerName..."
        $result = Invoke-Command -Session $session -ScriptBlock {
            param($Force)
            $params = @{}
            if ($Force) { $params.Force = $true }
            & "C:\temp\remove_asset_tag_simple.ps1" @params
        } -ArgumentList $Force
        
        Write-Log "Cleaning up on $ComputerName..."
        Invoke-Command -Session $session -ScriptBlock {
            Remove-Item -Path "C:\temp\remove_asset_tag_simple.ps1" -Force -ErrorAction SilentlyContinue
        }
        
        Remove-PSSession -Session $session
        
        Write-Log "SUCCESS: Deployment completed on $ComputerName"
        return $true
    }
    catch {
        Write-Log "ERROR: Deployment failed on $ComputerName - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Deploy-ToComputer-PsExec {
    param([String]$ComputerName)
    
    try {
        Write-Log "Testing connectivity to $ComputerName..."
        if (-not (Test-Connection -ComputerName $ComputerName -Count 1 -Quiet)) {
            throw "Computer not reachable"
        }
        
        Write-Log "Copying script to $ComputerName..."
        $remotePath = "\\$ComputerName\c$\temp\remove_asset_tag_simple.ps1"
        Copy-Item -Path $ScriptPath -Destination $remotePath -Force
        
        Write-Log "Executing script on $ComputerName..."
        $arguments = @(
            "\\$ComputerName"
            "-h"
            "-d"
            "powershell.exe"
            "-ExecutionPolicy"
            "Bypass"
            "-File"
            "C:\temp\remove_asset_tag_simple.ps1"
        )
        
        if ($Force) {
            $arguments += "-Force"
        }
        
        $process = Start-Process -FilePath "psexec.exe" -ArgumentList $arguments -Wait -PassThru -NoNewWindow
        
        Write-Log "Cleaning up on $ComputerName..."
        Remove-Item -Path $remotePath -Force -ErrorAction SilentlyContinue
        
        if ($process.ExitCode -eq 0) {
            Write-Log "SUCCESS: Deployment completed on $ComputerName"
            return $true
        }
        else {
            throw "PsExec returned exit code: $($process.ExitCode)"
        }
    }
    catch {
        Write-Log "ERROR: Deployment failed on $ComputerName - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Deploy-ToComputer-Local {
    try {
        Write-Log "Executing script locally..."
        
        $params = @{}
        if ($Force) { $params.Force = $true }
        
        & $ScriptPath @params
        
        Write-Log "SUCCESS: Local deployment completed"
        return $true
    }
    catch {
        Write-Log "ERROR: Local deployment failed - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Create-NetworkShareDeployment {
    $sharePath = Read-Host "Enter network share path (e.g., \\server\share\asset_removal)"
    
    if ([string]::IsNullOrWhiteSpace($sharePath)) {
        Write-Log "No share path provided. Exiting." "ERROR"
        return
    }
    
    try {
        Write-Log "Creating network share deployment at: $sharePath"
        
        if (-not (Test-Path $sharePath)) {
            New-Item -Path $sharePath -ItemType Directory -Force | Out-Null
        }
        
        Copy-Item -Path $ScriptPath -Destination $sharePath -Force
        Copy-Item -Path $PSCommandPath -Destination $sharePath -Force
        
        # Create simple runner script
        $runnerScript = @"
@echo off
cd /d "%~dp0"
powershell.exe -ExecutionPolicy Bypass -File "remove_asset_tag_simple.ps1" -Force
pause
"@
        $runnerScript | Out-File -FilePath "$sharePath\run_removal.bat" -Encoding ASCII
        
        Write-Log "Network share deployment created successfully!"
        Write-Log "You can now deploy using:"
        Write-Log "  - Group Policy: $sharePath\run_removal.bat"
        Write-Log "  - SCCM/Intune: Deploy the package from $sharePath"
        Write-Log "  - Manual: Copy and run from $sharePath"
    }
    catch {
        Write-Log "ERROR: Failed to create network share deployment - $($_.Exception.Message)" "ERROR"
    }
}

# Main execution
Write-Log "=== Asset Tag Removal Deployment Started ==="
Write-Log "Method: $Method"
Write-Log "Script: $ScriptPath"

if (-not (Test-Prerequisites)) {
    exit 1
}

if ($Method -eq 'NetworkShare') {
    Create-NetworkShareDeployment
    exit 0
}

if ($Method -eq 'Local') {
    Deploy-ToComputer-Local
    exit 0
}

# Get target computers
$targetComputers = Get-TargetComputers
if ($targetComputers.Count -eq 0) {
    Write-Log "No target computers specified. Exiting."
    exit 1
}

# Get credentials for remote deployment
if (-not $Credential -and $Method -in @('WinRM', 'PsExec')) {
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
    if (-not $Credential) {
        Write-Log "No credentials provided. Exiting." "ERROR"
        exit 1
    }
}

# Deploy to each computer
$successCount = 0
$totalCount = $targetComputers.Count

Write-Log "Deploying to $totalCount computers..."

foreach ($computer in $targetComputers) {
    Write-Log "Processing computer: $computer"
    
    $success = switch ($Method) {
        'WinRM' { Deploy-ToComputer-WinRM -ComputerName $computer }
        'PsExec' { Deploy-ToComputer-PsExec -ComputerName $computer }
    }
    
    if ($success) {
        $successCount++
    }
}

Write-Log "=== Deployment Summary ==="
Write-Log "Total computers: $totalCount"
Write-Log "Successful: $successCount"
Write-Log "Failed: $($totalCount - $successCount)"
Write-Log "=== Deployment Completed ==="
