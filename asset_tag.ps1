#Requires -RunAsAdministrator
 
[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [String]
    $AssetTag,
    [Parameter(Mandatory = $false)]
    [Switch]
    $VerifyWithUnsafeMethods # Probably won't work with Intune (will cause a timeout)
)
 
function ConvertTo-MultilineBase64([String]$FilePath, [Switch]$Compress) {
    if (Test-Path -Path $FilePath) {
        $TargetFilePath = $FilePath
 
        if ($Compress) {
            $TargetFilePath = "$($env:TEMP)\" + ([System.IO.Path]::GetFileName($TargetFilePath)) + ".zip"
            [void](Compress-Archive -Path $FilePath -DestinationPath $TargetFilePath)
        }
 
        $Text = "@""`n" + (([System.Convert]::ToBase64String([System.IO.File]::ReadAllBytes($TargetFilePath))) -replace '.{1024}', "`$&`n") + "`n""@"
        if ($Compress) {
            Remove-Item -Path $TargetFilePath -Force
        }
 
        return $Text
    }
}
 
# FilePath needs to be a folder if Compressed
function ConvertFrom-MultilineBase64([String]$FilePath, [String]$Text, [Switch]$Compressed) {
    if ($Text.StartsWith('@"') -and $Text.EndsWith('"@')) {
        $Text = $Text.Remove(0, 2)
        $Text = $Text.Remove($Text.Length - 2, 2).Trim()
    }
 
    $Bytes = [System.Convert]::FromBase64String($Text)
    if ($Compressed) {
        $ExpandTempFilePath = "$($env:TEMP)\" + ([System.IO.Path]::GetFileName($FilePath)) + ".zip"
        [System.IO.File]::WriteAllBytes($ExpandTempFilePath, $Bytes)
 
        [void](Expand-Archive -Path $ExpandTempFilePath -DestinationPath $FilePath)
 
        Remove-Item -Path $ExpandTempFilePath -Force
    }
    else {
        [System.IO.File]::WriteAllBytes($FilePath, $Bytes)
    }
 
}
 
# Getting asset tag displays only via messagebox
$GetMessageBoxText =
@"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
 
namespace GetMessageBoxText
{
    public class Unsafe
    {
        [DllImport("user32.dll", SetLastError = true)]
        public static extern IntPtr FindWindowEx(IntPtr parentHandle, IntPtr hWndChildAfter, string className, string windowTitle);
 
        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        static extern IntPtr SendMessage(IntPtr hWnd, UInt32 Msg, IntPtr wParam, [Out] StringBuilder lParam);
 
        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        static extern IntPtr SendMessage(IntPtr hWnd, UInt32 Msg, IntPtr wParam, IntPtr lParam);
 
        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
 
        const int WM_GETTEXT = 0x000D;
        const int WM_GETTEXTLENGTH = 0x000E;
        const int WM_CLOSE = 0x10;
 
        const int SW_MINIMIZE = 6;
 
        public static string GetWindowText(Process p, bool CloseWindow = false, int IgnoreTextLessThanLength = 2)
        {
            string messageBoxText = "";
            int waitForHandleCounter = 0;
            do
            {
                System.Threading.Thread.Sleep(1000);
                waitForHandleCounter++;
            } while (p.MainWindowHandle == IntPtr.Zero || waitForHandleCounter > 4);
            if (p.MainWindowHandle != IntPtr.Zero)
            {
                IntPtr parent = p.MainWindowHandle;
                IntPtr child = IntPtr.Zero;
 
                ShowWindow(parent, SW_MINIMIZE);
 
                do
                {
                    child = FindWindowEx(parent, child, null, null);
                    int windowTextLength = (int)SendMessage(child, WM_GETTEXTLENGTH, IntPtr.Zero, IntPtr.Zero);
                    StringBuilder sb = new StringBuilder(windowTextLength + 1);
                    SendMessage(child, WM_GETTEXT, (IntPtr)sb.Capacity, sb);
                    messageBoxText = sb.ToString();
 
                    if (messageBoxText.Length > IgnoreTextLessThanLength) { break; }
                } while (child != IntPtr.Zero);
 
                if (CloseWindow) { SendMessage(parent, WM_CLOSE, IntPtr.Zero, IntPtr.Zero); }
            }
 
            return messageBoxText;
        }
    }
}
"@
 
Add-Type -TypeDefinition $GetMessageBoxText -Language CSharp
 
# Doesn't appear to be any other way to set the asset tag
# https://support.lenovo.com/us/en/downloads/ds039503-windows-utility-to-read-and-write-asset-id-information-for-window-10-64-bit-81-64-bit-8-64-bit-7-32-bit-64-bit-thinkpad
$WinAIA64 = 
@"
COMPRESSED_BASE64_HERE
"@
 
$ExitCode = 0
$WinAIA64TempPath = "$($env:TEMP)\LenovoAssetUtil"
ConvertFrom-MultilineBase64 -FilePath $WinAIA64TempPath -Text $WinAIA64 -Compressed
 
$ExitCode = (Start-Process -FilePath "$WinAIA64TempPath\WinAIA64.exe" -ArgumentList @("-silent -set ""USERASSETDATA.ASSET_NUMBER=$AssetTag""") -WindowStyle Hidden -PassThru -Wait).ExitCode
if ($ExitCode -eq 0) {
    if ($VerifyWithUnsafeMethods) {
        $WinAIA64GetProc = Start-Process -FilePath "$WinAIA64TempPath\WinAIA64.exe" -ArgumentList @("-get USERASSETDATA") -WindowStyle Hidden -PassThru
        $UserAssetData = [GetMessageBoxText.Unsafe]::GetWindowText($WinAIA64GetProc, $true)
        $AssetNumber = [Regex]::Match($UserAssetData, "ASSET_NUMBER\s*=\s*(?<ASSET_NUMBER>.+)").Groups[1].Value
        if ($AssetTag -eq $AssetNumber) {
            Write-Output "Asset tag was set successfully!"
        }
        else {
            Write-Error -Message "Asset tag failed to set: Does not match after setting"
        }
 
        [void]($WinAIA64GetProc.WaitForExit(5000))
    }
    else {
        Write-Output "Asset tag was set successfully!"
    }
}
else {
    Write-Error -Message "Asset tag failed to set: Non-zero exit code $ExitCode"
}
 
# For some reason, the process is still running when deployed from Intune
Get-Process -Name "WinAIA64" -ErrorAction SilentlyContinue | Stop-Process -Force
Remove-Item -Path $WinAIA64TempPath -Force -Recurse
exit $ExitCode