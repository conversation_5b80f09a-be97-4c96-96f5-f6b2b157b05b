<#
.SYNOPSIS
    Group Policy deployment script for asset tag removal

.DESCRIPTION
    This script is designed to be deployed via Group Policy as a computer startup/shutdown script
    or as a scheduled task. It will run the asset tag removal script silently.

.NOTES
    - Place this script and remove_asset_tag_simple.ps1 in the SYSVOL share
    - Configure as a Computer Configuration > Windows Settings > Scripts > Startup script
    - Or deploy via Group Policy Preferences > Scheduled Tasks
#>

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Define paths
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$assetRemovalScript = Join-Path $scriptPath "remove_asset_tag_simple.ps1"
$logPath = "C:\Windows\Logs\AssetTagRemoval.log"

# Ensure log directory exists
$logDir = Split-Path -Parent $logPath
if (-not (Test-Path $logDir)) {
    New-Item -Path $logDir -ItemType Directory -Force | Out-Null
}

function Write-Log {
    param(
        [String]$Message,
        [String]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    try {
        Add-Content -Path $logPath -Value $logMessage -ErrorAction SilentlyContinue
    }
    catch {
        # If we can't write to log, continue silently
    }
}

try {
    Write-Log "Asset tag removal deployment started"
    Write-Log "Computer: $env:COMPUTERNAME"
    Write-Log "User: $env:USERNAME"
    Write-Log "Script path: $assetRemovalScript"
    
    # Check if the asset removal script exists
    if (-not (Test-Path $assetRemovalScript)) {
        Write-Log "Asset removal script not found at: $assetRemovalScript" "ERROR"
        exit 1
    }
    
    # Check if we're running as administrator
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    $isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    
    if (-not $isAdmin) {
        Write-Log "Script is not running as administrator" "WARNING"
        # Try to elevate if possible
        try {
            $arguments = "-ExecutionPolicy Bypass -File `"$($MyInvocation.MyCommand.Path)`""
            Start-Process PowerShell -Verb RunAs -ArgumentList $arguments -Wait
            Write-Log "Script elevated successfully"
            exit 0
        }
        catch {
            Write-Log "Failed to elevate script: $($_.Exception.Message)" "ERROR"
            exit 1
        }
    }
    
    Write-Log "Running asset tag removal script..."
    
    # Execute the asset tag removal script with Force parameter
    $result = & $assetRemovalScript -Force 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Log "Asset tag removal completed successfully"
        Write-Log "Output: $result"
    }
    else {
        Write-Log "Asset tag removal failed with exit code: $LASTEXITCODE" "ERROR"
        Write-Log "Error output: $result" "ERROR"
    }
    
    Write-Log "Asset tag removal deployment completed"
}
catch {
    Write-Log "Unexpected error during deployment: $($_.Exception.Message)" "ERROR"
    exit 1
}

exit $LASTEXITCODE
