#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Simple script to remove asset tags from Lenovo computers

.DESCRIPTION
    This script removes asset tags by using various methods depending on the computer manufacturer.
    For Lenovo computers, it uses WMI/CIM methods where possible, or falls back to the WinAIA64 utility.

.PARAMETER Force
    Skip confirmation prompt

.PARAMETER Method
    Specify the method to use: 'WMI', 'Registry', 'Utility'

.EXAMPLE
    .\remove_asset_tag_simple.ps1
    
.EXAMPLE
    .\remove_asset_tag_simple.ps1 -Force -Method WMI
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $false)]
    [Switch]
    $Force,
    
    [Parameter(Mandatory = $false)]
    [ValidateSet('WMI', 'Registry', 'Utility', 'Auto')]
    [String]
    $Method = 'Auto'
)

function Get-ComputerManufacturer {
    try {
        $manufacturer = (Get-CimInstance -ClassName Win32_ComputerSystem).Manufacturer
        return $manufacturer
    }
    catch {
        Write-Warning "Could not determine computer manufacturer: $($_.Exception.Message)"
        return "Unknown"
    }
}

function Get-CurrentAssetTag {
    try {
        # Try WMI first
        $assetTag = (Get-CimInstance -ClassName Win32_SystemEnclosure).SMBIOSAssetTag
        if (-not [string]::IsNullOrWhiteSpace($assetTag) -and $assetTag -ne "No Asset Information") {
            return $assetTag
        }
        
        # Try alternative WMI class
        $assetTag = (Get-CimInstance -ClassName Win32_ComputerSystem).Name
        if (-not [string]::IsNullOrWhiteSpace($assetTag)) {
            return $assetTag
        }
        
        return $null
    }
    catch {
        Write-Warning "Could not retrieve current asset tag: $($_.Exception.Message)"
        return $null
    }
}

function Remove-AssetTagWMI {
    Write-Output "Attempting to remove asset tag using WMI method..."
    
    try {
        # This method works for some manufacturers
        $enclosure = Get-CimInstance -ClassName Win32_SystemEnclosure
        $enclosure.SMBIOSAssetTag = ""
        Set-CimInstance -InputObject $enclosure
        
        Write-Output "Asset tag cleared using WMI method."
        return $true
    }
    catch {
        Write-Warning "WMI method failed: $($_.Exception.Message)"
        return $false
    }
}

function Remove-AssetTagRegistry {
    Write-Output "Attempting to remove asset tag using Registry method..."
    
    try {
        # Common registry locations for asset tags
        $registryPaths = @(
            "HKLM:\HARDWARE\DESCRIPTION\System\BIOS",
            "HKLM:\SYSTEM\CurrentControlSet\Services\mssmbios\Data",
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Group Policy\State\Machine\Extension-List"
        )
        
        $removed = $false
        foreach ($path in $registryPaths) {
            if (Test-Path $path) {
                $properties = @("AssetTag", "Asset Tag", "SystemAssetTag", "SMBIOSAssetTag")
                foreach ($prop in $properties) {
                    try {
                        if (Get-ItemProperty -Path $path -Name $prop -ErrorAction SilentlyContinue) {
                            Remove-ItemProperty -Path $path -Name $prop -Force
                            Write-Output "Removed asset tag from registry: $path\$prop"
                            $removed = $true
                        }
                    }
                    catch {
                        # Continue to next property
                    }
                }
            }
        }
        
        if ($removed) {
            Write-Output "Asset tag removed using Registry method."
            return $true
        }
        else {
            Write-Warning "No asset tag found in common registry locations."
            return $false
        }
    }
    catch {
        Write-Warning "Registry method failed: $($_.Exception.Message)"
        return $false
    }
}

function Remove-AssetTagUtility {
    Write-Output "Utility method requires the WinAIA64.exe or manufacturer-specific utility."
    Write-Output "Please use the full remove_asset_tag.ps1 script with the embedded utility."
    return $false
}

# Main script execution
Write-Output "Asset Tag Removal Script"
Write-Output "========================"

# Get computer info
$manufacturer = Get-ComputerManufacturer
Write-Output "Computer Manufacturer: $manufacturer"

# Get current asset tag
$currentAssetTag = Get-CurrentAssetTag
if ([string]::IsNullOrWhiteSpace($currentAssetTag) -or $currentAssetTag -eq "No Asset Information") {
    Write-Output "No asset tag is currently set or asset tag is already empty."
    exit 0
}

Write-Output "Current Asset Tag: $currentAssetTag"

# Confirmation prompt (unless -Force is used)
if (-not $Force) {
    Write-Output ""
    $confirmation = Read-Host "Are you sure you want to remove the asset tag '$currentAssetTag'? Type 'YES' to confirm"
    if ($confirmation -ne 'YES') {
        Write-Output "Asset tag removal cancelled."
        exit 0
    }
}

Write-Output ""
Write-Output "Starting asset tag removal process..."

# Determine method to use
$success = $false

if ($Method -eq 'Auto') {
    # Try methods in order of preference
    $success = Remove-AssetTagWMI
    if (-not $success) {
        $success = Remove-AssetTagRegistry
    }
    if (-not $success) {
        $success = Remove-AssetTagUtility
    }
}
else {
    switch ($Method) {
        'WMI' { $success = Remove-AssetTagWMI }
        'Registry' { $success = Remove-AssetTagRegistry }
        'Utility' { $success = Remove-AssetTagUtility }
    }
}

# Verify removal
Write-Output ""
if ($success) {
    Write-Output "Verifying asset tag removal..."
    Start-Sleep -Seconds 2
    
    $newAssetTag = Get-CurrentAssetTag
    if ([string]::IsNullOrWhiteSpace($newAssetTag) -or $newAssetTag -eq "No Asset Information") {
        Write-Output "SUCCESS: Asset tag has been removed successfully!"
        exit 0
    }
    else {
        Write-Warning "Asset tag may still be present: $newAssetTag"
        Write-Output "You may need to restart the computer for changes to take effect."
        exit 1
    }
}
else {
    Write-Error "FAILED: Could not remove asset tag using available methods."
    Write-Output ""
    Write-Output "Suggestions:"
    Write-Output "1. Try running the full remove_asset_tag.ps1 script with the manufacturer utility"
    Write-Output "2. Use manufacturer-specific tools (e.g., Lenovo's WinAIA64.exe)"
    Write-Output "3. Check BIOS/UEFI settings for asset tag configuration"
    Write-Output "4. Contact your system administrator"
    exit 1
}
