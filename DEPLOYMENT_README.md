# Asset Tag Removal - Deployment Guide

This guide explains how to deploy the asset tag removal script to multiple computers using various methods.

## Files Overview

- `remove_asset_tag_simple.ps1` - Main asset tag removal script
- `Deploy-AssetTagRemoval.ps1` - PowerShell deployment script
- `deploy_remove_asset_tag.bat` - Batch deployment script
- `GPO-Deploy-AssetTagRemoval.ps1` - Group Policy deployment script
- `computers.txt` - List of target computers

## Deployment Methods

### 1. PowerShell Deployment (Recommended)

**Prerequisites:**
- PowerShell 5.0 or later
- Administrator privileges
- WinRM enabled on target computers (for WinRM method)
- PsExec downloaded (for PsExec method)

**Usage:**

```powershell
# Deploy to specific computers using WinRM
.\Deploy-AssetTagRemoval.ps1 -ComputerNames @("PC01", "PC02") -Method WinRM

# Deploy to computers listed in computers.txt using PsExec
.\Deploy-AssetTagRemoval.ps1 -ComputerListFile "computers.txt" -Method PsExec

# Deploy locally
.\Deploy-AssetTagRemoval.ps1 -Method Local

# Create network share deployment
.\Deploy-AssetTagRemoval.ps1 -Method NetworkShare
```

### 2. Batch Script Deployment

**Usage:**
1. Run `deploy_remove_asset_tag.bat` as Administrator
2. Choose deployment method:
   - Option 1: Deploy to computers in computers.txt
   - Option 2: Deploy to single computer
   - Option 3: Deploy locally
   - Option 4: Create network share

**Prerequisites for remote deployment:**
- PsExec in PATH or same directory
- Administrative access to target computers
- File sharing enabled on target computers

### 3. Group Policy Deployment

**Setup:**
1. Copy scripts to SYSVOL share: `\\domain.com\SYSVOL\domain.com\scripts\`
2. Open Group Policy Management Console
3. Create or edit a Group Policy Object
4. Navigate to: Computer Configuration > Policies > Windows Settings > Scripts
5. Add `GPO-Deploy-AssetTagRemoval.ps1` as a Startup or Shutdown script

**Alternative - Scheduled Task via GPO:**
1. Computer Configuration > Preferences > Control Panel Settings > Scheduled Tasks
2. Create new scheduled task
3. Set to run: `powershell.exe -ExecutionPolicy Bypass -File "\\server\share\GPO-Deploy-AssetTagRemoval.ps1"`
4. Configure trigger (e.g., at startup, daily, etc.)

### 4. SCCM/ConfigMgr Deployment

**Package Creation:**
1. Create new package in SCCM console
2. Source files: All deployment scripts
3. Program command line: `powershell.exe -ExecutionPolicy Bypass -File "remove_asset_tag_simple.ps1" -Force`
4. Program requirements: Administrator rights
5. Deploy to target collection

### 5. Microsoft Intune Deployment

**PowerShell Script Deployment:**
1. In Intune admin center, go to Devices > Scripts
2. Add new PowerShell script
3. Upload `remove_asset_tag_simple.ps1`
4. Configure settings:
   - Run this script using the logged on credentials: No
   - Enforce script signature check: No
   - Run script in 64-bit PowerShell Host: Yes
5. Assign to device groups

### 6. Network Share Deployment

**Setup:**
1. Create network share accessible by target computers
2. Copy all scripts to the share
3. Use one of these methods:

**Option A - Manual execution:**
```batch
\\server\share\run_removal.bat
```

**Option B - Remote execution:**
```batch
psexec \\computer -c "\\server\share\run_removal.bat"
```

**Option C - Scheduled task:**
Create scheduled task pointing to network share script

## Configuration

### computers.txt Format
```
# Comments start with #
COMPUTER01
COMPUTER02
*************
LAPTOP-USER1
```

### Credentials
For remote deployment methods, you'll be prompted for credentials or can provide them:

```powershell
$cred = Get-Credential
.\Deploy-AssetTagRemoval.ps1 -ComputerNames @("PC01") -Method WinRM -Credential $cred
```

## Troubleshooting

### Common Issues

**1. Access Denied**
- Ensure running as Administrator
- Check firewall settings
- Verify credentials have admin rights on target computers

**2. WinRM Errors**
- Enable WinRM: `winrm quickconfig`
- Configure trusted hosts: `winrm set winrm/config/client '@{TrustedHosts="*"}'`
- Check Windows Remote Management service is running

**3. PsExec Issues**
- Download PsExec from Microsoft Sysinternals
- Accept EULA: `psexec -accepteula`
- Ensure target computers allow file sharing

**4. PowerShell Execution Policy**
- Set execution policy: `Set-ExecutionPolicy RemoteSigned`
- Or use bypass: `powershell -ExecutionPolicy Bypass -File script.ps1`

### Logs
- PowerShell deployment: `deployment_log.txt`
- Group Policy deployment: `C:\Windows\Logs\AssetTagRemoval.log`
- Local execution: Console output

## Security Considerations

1. **Credentials**: Use service accounts with minimal required privileges
2. **Network**: Deploy over secure networks or VPN
3. **Validation**: Test on non-production systems first
4. **Backup**: Document current asset tags before removal
5. **Audit**: Review logs after deployment

## Testing

Before mass deployment:

1. Test on a single computer:
```powershell
.\remove_asset_tag_simple.ps1 -Force
```

2. Test remote deployment to one computer:
```powershell
.\Deploy-AssetTagRemoval.ps1 -ComputerNames @("TEST-PC") -Method WinRM
```

3. Verify asset tag removal:
```powershell
Get-CimInstance -ClassName Win32_SystemEnclosure | Select-Object SMBIOSAssetTag
```

## Support

For issues or questions:
1. Check logs for error details
2. Verify prerequisites are met
3. Test connectivity to target computers
4. Ensure proper permissions and credentials
