@echo off
REM ============================================================================
REM Asset Tag Removal Deployment Script
REM This batch script deploys and runs the asset tag removal script on multiple PCs
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration
set "SCRIPT_NAME=remove_asset_tag_simple.ps1"
set "LOG_FILE=%~dp0deployment_log.txt"
set "COMPUTERS_FILE=%~dp0computers.txt"

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ============================================================================
echo Asset Tag Removal Deployment Script
echo ============================================================================
echo.

REM Check if PowerShell script exists
if not exist "%~dp0%SCRIPT_NAME%" (
    echo ERROR: PowerShell script '%SCRIPT_NAME%' not found in current directory!
    echo Please ensure the script is in the same folder as this batch file.
    pause
    exit /b 1
)

REM Initialize log file
echo Deployment started at %date% %time% > "%LOG_FILE%"
echo. >> "%LOG_FILE%"

echo Select deployment method:
echo 1. Deploy to computers listed in computers.txt
echo 2. Deploy to a single computer (enter computer name)
echo 3. Deploy to current computer only
echo 4. Create network share deployment
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto :deploy_from_file
if "%choice%"=="2" goto :deploy_single
if "%choice%"=="3" goto :deploy_local
if "%choice%"=="4" goto :create_network_share
echo Invalid choice. Exiting.
pause
exit /b 1

:deploy_from_file
echo.
echo Deploying to computers listed in %COMPUTERS_FILE%
echo.

if not exist "%COMPUTERS_FILE%" (
    echo Creating sample computers.txt file...
    echo REM List computer names or IP addresses, one per line > "%COMPUTERS_FILE%"
    echo REM Example: >> "%COMPUTERS_FILE%"
    echo REM COMPUTER01 >> "%COMPUTERS_FILE%"
    echo REM COMPUTER02 >> "%COMPUTERS_FILE%"
    echo REM ************* >> "%COMPUTERS_FILE%"
    echo.
    echo Sample computers.txt file created. Please edit it with your computer names/IPs.
    pause
    exit /b 0
)

for /f "usebackq tokens=* delims=" %%a in ("%COMPUTERS_FILE%") do (
    set "line=%%a"
    REM Skip empty lines and comments
    if not "!line!"=="" if not "!line:~0,3!"=="REM" (
        call :deploy_to_computer "%%a"
    )
)
goto :end

:deploy_single
echo.
set /p computer_name="Enter computer name or IP address: "
if "%computer_name%"=="" (
    echo No computer name entered. Exiting.
    pause
    exit /b 1
)
call :deploy_to_computer "%computer_name%"
goto :end

:deploy_local
echo.
echo Deploying to local computer...
call :run_local_script
goto :end

:create_network_share
echo.
echo Creating network share deployment package...
set /p share_path="Enter network share path (e.g., \\server\share\asset_removal): "
if "%share_path%"=="" (
    echo No share path entered. Exiting.
    pause
    exit /b 1
)

mkdir "%share_path%" 2>nul
copy "%~dp0%SCRIPT_NAME%" "%share_path%\" >nul
copy "%~dp0deploy_remove_asset_tag.bat" "%share_path%\" >nul

REM Create a simple deployment script for the share
echo @echo off > "%share_path%\run_removal.bat"
echo cd /d "%%~dp0" >> "%share_path%\run_removal.bat"
echo powershell.exe -ExecutionPolicy Bypass -File "%SCRIPT_NAME%" -Force >> "%share_path%\run_removal.bat"
echo pause >> "%share_path%\run_removal.bat"

echo.
echo Network share deployment package created at: %share_path%
echo.
echo To deploy to remote computers, you can now:
echo 1. Use Group Policy to run: %share_path%\run_removal.bat
echo 2. Use PsExec: psexec \\computer -c "%share_path%\run_removal.bat"
echo 3. Use SCCM/Intune to deploy the package
echo.
pause
goto :end

:deploy_to_computer
set "target_computer=%~1"
echo.
echo ----------------------------------------
echo Deploying to: %target_computer%
echo ----------------------------------------

REM Test connectivity
ping -n 1 "%target_computer%" >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Cannot reach %target_computer%
    echo ERROR: Cannot reach %target_computer% >> "%LOG_FILE%"
    goto :eof
)

REM Copy script to remote computer
echo Copying script to %target_computer%...
if not exist "\\%target_computer%\c$\temp" (
    echo ERROR: Cannot access C:\temp on %target_computer% - check permissions
    echo ERROR: Cannot access C:\temp on %target_computer% - check permissions >> "%LOG_FILE%"
    goto :eof
)

copy "%~dp0%SCRIPT_NAME%" "\\%target_computer%\c$\temp\" >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Failed to copy script to %target_computer%
    echo ERROR: Failed to copy script to %target_computer% >> "%LOG_FILE%"
    goto :eof
)

REM Execute script on remote computer
echo Executing script on %target_computer%...
psexec \\%target_computer% -h -d powershell.exe -ExecutionPolicy Bypass -File "C:\temp\%SCRIPT_NAME%" -Force

if %errorLevel% equ 0 (
    echo SUCCESS: Script executed on %target_computer%
    echo SUCCESS: Script executed on %target_computer% at %time% >> "%LOG_FILE%"
) else (
    echo ERROR: Script execution failed on %target_computer%
    echo ERROR: Script execution failed on %target_computer% at %time% >> "%LOG_FILE%"
)

REM Clean up
del "\\%target_computer%\c$\temp\%SCRIPT_NAME%" >nul 2>&1

goto :eof

:run_local_script
echo Running script on local computer...
powershell.exe -ExecutionPolicy Bypass -File "%~dp0%SCRIPT_NAME%" -Force
if %errorLevel% equ 0 (
    echo SUCCESS: Script executed locally
    echo SUCCESS: Script executed locally at %time% >> "%LOG_FILE%"
) else (
    echo ERROR: Local script execution failed
    echo ERROR: Local script execution failed at %time% >> "%LOG_FILE%"
)
goto :eof

:end
echo.
echo ============================================================================
echo Deployment completed. Check %LOG_FILE% for details.
echo ============================================================================
pause
exit /b 0
